#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户模型
"""

import sqlite3
from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from models.database import get_db_connection, get_db_cursor, log_user_action

class User(UserMixin):
    """用户类"""
    
    def __init__(self, id, username, password_hash, email=None, full_name=None, 
                 role='student', student_id=None, is_active=True, is_sub_admin=False,
                 created_at=None, updated_at=None, last_login=None):
        self.id = id
        self.username = username
        self.password_hash = password_hash
        self.email = email
        self.full_name = full_name
        self.role = role
        self.student_id = student_id
        self.is_active = is_active
        self.is_sub_admin = is_sub_admin
        self.created_at = created_at
        self.updated_at = updated_at
        self.last_login = last_login
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def is_admin(self):
        """是否为管理员"""
        return self.role == 'admin'
    
    def is_teacher(self):
        """是否为教师"""
        return self.role == 'teacher'
    
    def is_student(self):
        """是否为学生"""
        return self.role == 'student'
    
    def has_admin_permission(self):
        """是否有管理员权限（管理员或子管理员）"""
        return self.role == 'admin' or (self.role == 'teacher' and self.is_sub_admin)
    
    def can_access_admin_panel(self):
        """是否可以访问管理面板"""
        return self.role == 'admin'
    
    def can_modify_data(self):
        """是否可以修改数据"""
        return self.role == 'admin' or (self.role == 'teacher' and self.is_sub_admin)
    
    def update_last_login(self):
        """更新最后登录时间"""
        try:
            with get_db_cursor() as cursor:
                cursor.execute('''
                    UPDATE users SET last_login = CURRENT_TIMESTAMP 
                    WHERE id = ?
                ''', (self.id,))
                self.last_login = datetime.now()
        except Exception as e:
            print(f"更新登录时间失败: {e}")
    
    @staticmethod
    def get_by_id(user_id):
        """根据ID获取用户"""
        try:
            with get_db_cursor() as cursor:
                cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
                row = cursor.fetchone()
                if row:
                    return User(**dict(row))
                return None
        except Exception as e:
            print(f"获取用户失败: {e}")
            return None
    
    @staticmethod
    def get_by_username(username):
        """根据用户名获取用户"""
        try:
            with get_db_cursor() as cursor:
                cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
                row = cursor.fetchone()
                if row:
                    return User(**dict(row))
                return None
        except Exception as e:
            print(f"获取用户失败: {e}")
            return None
    
    @staticmethod
    def get_by_student_id(student_id):
        """根据学号获取用户"""
        try:
            with get_db_cursor() as cursor:
                cursor.execute('SELECT * FROM users WHERE student_id = ?', (student_id,))
                row = cursor.fetchone()
                if row:
                    return User(**dict(row))
                return None
        except Exception as e:
            print(f"获取用户失败: {e}")
            return None
    
    @staticmethod
    def create_user(username, password, email=None, full_name=None, role='student', student_id=None):
        """创建新用户"""
        try:
            password_hash = generate_password_hash(password)
            with get_db_cursor() as cursor:
                cursor.execute('''
                    INSERT INTO users (username, password_hash, email, full_name, role, student_id)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (username, password_hash, email, full_name, role, student_id))
                user_id = cursor.lastrowid
                
                # 记录日志
                log_user_action(user_id, 'user_created', f'创建用户: {username}')
                
                return User.get_by_id(user_id)
        except Exception as e:
            print(f"创建用户失败: {e}")
            return None
    
    @staticmethod
    def get_all_users(role=None):
        """获取所有用户"""
        try:
            with get_db_cursor() as cursor:
                if role:
                    cursor.execute('SELECT * FROM users WHERE role = ? ORDER BY created_at DESC', (role,))
                else:
                    cursor.execute('SELECT * FROM users ORDER BY created_at DESC')
                rows = cursor.fetchall()
                return [User(**dict(row)) for row in rows]
        except Exception as e:
            print(f"获取用户列表失败: {e}")
            return []
    
    def save(self):
        """保存用户信息"""
        try:
            with get_db_cursor() as cursor:
                cursor.execute('''
                    UPDATE users SET 
                        username = ?, password_hash = ?, email = ?, full_name = ?,
                        role = ?, student_id = ?, is_active = ?, is_sub_admin = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (self.username, self.password_hash, self.email, self.full_name,
                      self.role, self.student_id, self.is_active, self.is_sub_admin, self.id))
                
                # 记录日志
                log_user_action(self.id, 'user_updated', f'更新用户信息: {self.username}')
                
                return True
        except Exception as e:
            print(f"保存用户信息失败: {e}")
            return False
    
    def delete(self):
        """删除用户"""
        try:
            with get_db_cursor() as cursor:
                cursor.execute('DELETE FROM users WHERE id = ?', (self.id,))
                
                # 记录日志
                log_user_action(self.id, 'user_deleted', f'删除用户: {self.username}')
                
                return True
        except Exception as e:
            print(f"删除用户失败: {e}")
            return False
