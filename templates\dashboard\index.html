{% extends "base.html" %}

{% block title %}主面板 - 学生学业数据管理与分析平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            数据概览
        </h1>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.students }}</h4>
                        <p class="card-text">学生总数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.scores }}</h4>
                        <p class="card-text">成绩记录</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.semesters }}</h4>
                        <p class="card-text">学期数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.majors }}</h4>
                        <p class="card-text">专业数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-graduation-cap fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    专业分布
                </h5>
            </div>
            <div class="card-body">
                <div id="majorChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    年级分布
                </h5>
            </div>
            <div class="card-body">
                <div id="gradeChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 成绩趋势 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    成绩趋势
                </h5>
            </div>
            <div class="card-body">
                <div id="trendChart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
{% if current_user.has_admin_permission() %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    最近活动
                </h5>
            </div>
            <div class="card-body">
                <div id="recentActivities">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // 加载统计数据并绘制图表
    loadStatistics();
    
    {% if current_user.has_admin_permission() %}
    // 加载最近活动
    loadRecentActivities();
    {% endif %}
});

function loadStatistics() {
    $.get('/dashboard/api/statistics')
        .done(function(response) {
            if (response.success) {
                drawMajorChart(response.data.major_distribution);
                drawGradeChart(response.data.grade_distribution);
                drawTrendChart(response.data.semester_stats);
            }
        })
        .fail(function() {
            console.error('加载统计数据失败');
        });
}

function drawMajorChart(data) {
    const chart = echarts.init(document.getElementById('majorChart'));
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
            name: '专业分布',
            type: 'pie',
            radius: '70%',
            data: data.map(item => ({
                name: item.major_name,
                value: item.count
            })),
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };
    chart.setOption(option);
}

function drawGradeChart(data) {
    const chart = echarts.init(document.getElementById('gradeChart'));
    const option = {
        tooltip: {
            trigger: 'axis'
        },
        xAxis: {
            type: 'category',
            data: data.map(item => item.grade + '级')
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            name: '学生数量',
            type: 'bar',
            data: data.map(item => item.count),
            itemStyle: {
                color: '#4facfe'
            }
        }]
    };
    chart.setOption(option);
}

function drawTrendChart(data) {
    const chart = echarts.init(document.getElementById('trendChart'));
    const option = {
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['平均总分', '平均学业成绩', '平均综合素质分']
        },
        xAxis: {
            type: 'category',
            data: data.map(item => item.academic_year + '-' + item.semester)
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '平均总分',
                type: 'line',
                data: data.map(item => item.avg_total_score ? item.avg_total_score.toFixed(2) : 0)
            },
            {
                name: '平均学业成绩',
                type: 'line',
                data: data.map(item => item.avg_academic_score ? item.avg_academic_score.toFixed(2) : 0)
            },
            {
                name: '平均综合素质分',
                type: 'line',
                data: data.map(item => item.avg_comprehensive_score ? item.avg_comprehensive_score.toFixed(2) : 0)
            }
        ]
    };
    chart.setOption(option);
}

{% if current_user.has_admin_permission() %}
function loadRecentActivities() {
    $.get('/dashboard/api/recent_activities')
        .done(function(response) {
            if (response.success) {
                displayRecentActivities(response.data);
            }
        })
        .fail(function() {
            $('#recentActivities').html('<p class="text-muted">加载失败</p>');
        });
}

function displayRecentActivities(activities) {
    let html = '';
    if (activities.length === 0) {
        html = '<p class="text-muted">暂无活动记录</p>';
    } else {
        html = '<div class="list-group">';
        activities.forEach(function(activity) {
            html += `
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${activity.action}</h6>
                        <small>${activity.created_at}</small>
                    </div>
                    <p class="mb-1">${activity.description || ''}</p>
                    <small>用户: ${activity.full_name || activity.username || '未知'}</small>
                </div>
            `;
        });
        html += '</div>';
    }
    $('#recentActivities').html(html);
}
{% endif %}
</script>
{% endblock %}
