# 奖学金数据分析系统数据库结构说明

数据库 `scholarship_data.db` 旨在存储和管理从Excel文件解析的奖学金相关数据。其结构设计如下，以支持学生成绩的追踪、查询和分析：

**数据库文件**: `scholarship_data.db`

**表结构说明**:

1. **`semesters` (学期信息表)**

   * **用途**: 存储唯一的学期信息，作为 `scores` 表的外键。
   * **字段**:
     * `id` (INTEGER PRIMARY KEY AUTOINCREMENT): 唯一标识符。
     * `academic_year` (TEXT NOT NULL): 学年，例如 "2022-2023"。
     * `semester` (INTEGER NOT NULL): 学期，1代表第一学期，2代表第二学期。
     * `created_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP): 记录创建时间。
     * **唯一约束**: `(academic_year, semester)` 组合唯一。
2. **`students` (学生基本信息表)**

   * **用途**: 存储学生的基本信息，作为 `scores` 表的外键。
   * **字段**:
     * `id` (INTEGER PRIMARY KEY AUTOINCREMENT): 唯一标识符。
     * `student_id` (TEXT NOT NULL UNIQUE): 学生学号，唯一标识一个学生。
     * `name` (TEXT NOT NULL): 学生姓名。
     * `created_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP): 记录创建时间。
     * `updated_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP): 记录最后更新时间（当学生信息如姓名发生变化时更新）。
3. **`majors` (专业信息表)**

   * **用途**: 存储专业信息，作为 `scores` 表的外键。
   * **字段**:
     * `id` (INTEGER PRIMARY KEY AUTOINCREMENT): 唯一标识符。
     * `major_name` (TEXT NOT NULL UNIQUE): 专业名称，例如 "计算机科学与技术"。
     * `created_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP): 记录创建时间。
4. **`scores` (成绩记录表)**

   * **用途**: 存储每个学生在特定学期和专业下的详细成绩和评定结果。
   * **字段**:
     * `id` (INTEGER PRIMARY KEY AUTOINCREMENT): 唯一标识符。
     * `student_id` (TEXT NOT NULL): 学生学号，关联 `students` 表。
     * `semester_id` (INTEGER NOT NULL): 学期ID，关联 `semesters` 表。
     * `major_id` (INTEGER NOT NULL): 专业ID，关联 `majors` 表。
     * `class_name` (TEXT): 学生所在班级。
     * `grade` (INTEGER): 学生年级。
     * `moral_score` (REAL DEFAULT 0): 思想品德评估分数 (上限25分)。
     * `social_work_score` (REAL DEFAULT 0): 社会工作分数 (上限15分)。
     * `research_score` (REAL DEFAULT 0): 科研及科技创新分数 (上限25分)。
     * `activity_base_score` (REAL DEFAULT 0): 大型集体活动基础分。
     * `activity_exercise_score` (REAL DEFAULT 0): 早操/晚自习分数。
     * `activity_event_score` (REAL DEFAULT 0): 未设奖/设奖活动分数。
     * `activity_total_score` (REAL DEFAULT 0): 大型集体活动总分 (上限20分)。
     * `collective_score` (REAL DEFAULT 0): 集体建设分数 (上限15分)。
     * `comprehensive_score` (REAL DEFAULT 0): 综合素质测评总成绩。
     * `academic_score` (REAL DEFAULT 0): 学业成绩。
     * `academic_rank` (INTEGER): 学业成绩排名。
     * `academic_percentage` (REAL): 学业成绩排名百分比。
     * `comprehensive_weighted` (REAL DEFAULT 0): 综合素质测评总成绩 × 35%。
     * `academic_weighted` (REAL DEFAULT 0): 学业成绩 × 65%。
     * `total_score` (REAL DEFAULT 0): 总评 (P列 + Q列)。
     * `total_rank` (INTEGER): 总评专业排名。
     * `remarks` (TEXT): 备注信息。
     * `award_level` (TEXT): 拟定等级。
     * `created_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP): 记录创建时间。
     * `updated_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP): 记录最后更新时间。
     * **唯一约束**: `(student_id, semester_id, major_id)` 组合唯一，用于增量更新和追踪学生在不同学期和专业下的成绩。
     * **外键**: `semester_id` 引用 `semesters.id`，`major_id` 引用 `majors.id`。
5. **`file_records` (文件处理记录表)**

   * **用途**: 记录已处理的Excel文件，用于实现增量更新，避免重复导入。
   * **字段**:
     * `id` (INTEGER PRIMARY KEY AUTOINCREMENT): 唯一标识符。
     * `file_path` (TEXT NOT NULL UNIQUE): 已处理文件的完整路径。
     * `file_hash` (TEXT): 文件的MD5哈希值，用于检测文件内容是否发生变化。
     * `processed_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP): 文件处理时间。
     * `record_count` (INTEGER DEFAULT 0): 该文件导入的记录数量。

**数据处理逻辑**:

* 程序通过读取Excel文件第一行的标题来提取学年、学期、年级和专业信息。
* 从Excel的第5行开始解析学生数据，并根据预定义的列索引映射到数据库字段。
* 在导入各项评分时，程序会自动应用预设的上限值，例如“思想品德评估”最高25分，“大型集体活动”最高20分等。
* 通过 `INSERT OR REPLACE` 语句，程序支持对现有记录进行更新，确保数据的增量导入和准确性。

**查询示例**:
您可以使用 `ScholarshipAnalyzer` 类中的 `query_student_scores` 和 `get_top_students` 方法来查询数据。例如：

* `analyzer.query_student_scores(student_id="202412434")`：查询特定学号学生的所有成绩。
* `analyzer.get_top_students(academic_year="2024-2025", semester=1, major="物联网", limit=10)`：查询2024-2025学年第一学期物联网专业的前10名学生。

这个数据库结构能够有效地存储和管理奖学金数据，并支持后续的数据分析和报告生成。
