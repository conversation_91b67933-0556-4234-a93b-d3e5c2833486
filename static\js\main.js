// 主要JavaScript文件

$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // 自动隐藏警告框
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
    
    // 添加淡入动画
    $('.card').addClass('fade-in-up');
});

// 通用AJAX错误处理
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    if (xhr.status === 401) {
        window.location.href = '/auth/login';
    } else if (xhr.status === 403) {
        showAlert('权限不足', 'error');
    } else if (xhr.status >= 500) {
        showAlert('服务器错误，请稍后重试', 'error');
    }
});

// 显示警告框
function showAlert(message, type = 'info') {
    const alertClass = type === 'error' ? 'danger' : type;
    const alertHtml = `
        <div class="alert alert-${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在页面顶部插入警告框
    $('main .container-fluid').prepend(alertHtml);
    
    // 自动隐藏
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
}

// 显示加载状态
function showLoading(element) {
    const loadingHtml = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    `;
    $(element).html(loadingHtml);
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 格式化数字
function formatNumber(number, decimals = 2) {
    if (number === null || number === undefined) return '-';
    return parseFloat(number).toFixed(decimals);
}

// 格式化排名
function formatRank(rank) {
    if (!rank) return '-';
    
    let badge = '';
    if (rank === 1) {
        badge = '<i class="fas fa-trophy text-warning me-1"></i>';
    } else if (rank === 2) {
        badge = '<i class="fas fa-medal text-secondary me-1"></i>';
    } else if (rank === 3) {
        badge = '<i class="fas fa-award text-warning me-1"></i>';
    }
    
    return badge + rank;
}

// 获取奖学金等级徽章
function getAwardBadge(level) {
    if (!level || level === '无') return '<span class="badge bg-secondary">无</span>';
    
    const badgeMap = {
        '一等奖学金': 'bg-danger',
        '二等奖学金': 'bg-warning',
        '三等奖学金': 'bg-info',
        '优秀学生': 'bg-success'
    };
    
    const badgeClass = badgeMap[level] || 'bg-primary';
    return `<span class="badge ${badgeClass}">${level}</span>`;
}

// 导出数据为CSV
function exportToCSV(data, filename) {
    if (!data || data.length === 0) {
        showAlert('没有数据可导出', 'warning');
        return;
    }
    
    // 构建CSV内容
    const headers = Object.keys(data[0]);
    let csvContent = headers.join(',') + '\n';
    
    data.forEach(row => {
        const values = headers.map(header => {
            let value = row[header] || '';
            // 如果值包含逗号或引号，需要用引号包围
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                value = '"' + value.replace(/"/g, '""') + '"';
            }
            return value;
        });
        csvContent += values.join(',') + '\n';
    });
    
    // 创建下载链接
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 复制文本到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(function() {
            showAlert('已复制到剪贴板', 'success');
        }).catch(function() {
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showAlert('已复制到剪贴板', 'success');
        } else {
            showAlert('复制失败', 'error');
        }
    } catch (err) {
        showAlert('复制失败', 'error');
    }
    
    document.body.removeChild(textArea);
}

// 确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 防抖函数
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction() {
        const context = this;
        const args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 表格排序功能
function initTableSort(tableId) {
    $(`#${tableId} th[data-sort]`).click(function() {
        const column = $(this).data('sort');
        const order = $(this).hasClass('asc') ? 'desc' : 'asc';
        
        // 移除其他列的排序标识
        $(`#${tableId} th`).removeClass('asc desc');
        $(this).addClass(order);
        
        // 触发排序事件
        $(document).trigger('table-sort', {
            column: column,
            order: order
        });
    });
}

// 分页功能
function initPagination(containerId, totalPages, currentPage, callback) {
    let paginationHtml = '<nav><ul class="pagination justify-content-center">';
    
    // 上一页
    if (currentPage > 1) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${currentPage - 1}">上一页</a></li>`;
    }
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        paginationHtml += '<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>';
        if (startPage > 2) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'active' : '';
        paginationHtml += `<li class="page-item ${activeClass}"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a></li>`;
    }
    
    // 下一页
    if (currentPage < totalPages) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${currentPage + 1}">下一页</a></li>`;
    }
    
    paginationHtml += '</ul></nav>';
    
    $(`#${containerId}`).html(paginationHtml);
    
    // 绑定点击事件
    $(`#${containerId} .page-link`).click(function(e) {
        e.preventDefault();
        const page = parseInt($(this).data('page'));
        if (page && page !== currentPage) {
            callback(page);
        }
    });
}

// 搜索建议功能
function initSearchSuggestions(inputId, apiUrl) {
    const searchInput = $(`#${inputId}`);
    const suggestionsContainer = $('<div class="search-suggestions"></div>');
    searchInput.after(suggestionsContainer);
    
    const debouncedSearch = debounce(function(query) {
        if (query.length < 2) {
            suggestionsContainer.hide();
            return;
        }
        
        $.get(apiUrl, { q: query })
            .done(function(response) {
                if (response.success && response.data.length > 0) {
                    let html = '<ul class="list-group">';
                    response.data.forEach(item => {
                        html += `<li class="list-group-item list-group-item-action" data-value="${item.value}">${item.label}</li>`;
                    });
                    html += '</ul>';
                    suggestionsContainer.html(html).show();
                } else {
                    suggestionsContainer.hide();
                }
            });
    }, 300);
    
    searchInput.on('input', function() {
        debouncedSearch($(this).val());
    });
    
    // 点击建议项
    suggestionsContainer.on('click', '.list-group-item', function() {
        const value = $(this).data('value');
        const label = $(this).text();
        searchInput.val(label);
        suggestionsContainer.hide();
        
        // 触发选择事件
        searchInput.trigger('suggestion-selected', { value: value, label: label });
    });
    
    // 点击其他地方隐藏建议
    $(document).click(function(e) {
        if (!$(e.target).closest('.search-suggestions, #' + inputId).length) {
            suggestionsContainer.hide();
        }
    });
}
