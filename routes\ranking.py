#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排行榜路由
处理排行榜显示、排序、导出等功能
"""

import csv
import io
from flask import Blueprint, render_template, request, jsonify, make_response, flash
from flask_login import login_required, current_user
from models.scholarship import ScholarshipData
from models.database import log_user_action

ranking_bp = Blueprint('ranking', __name__)

@ranking_bp.route('/')
@login_required
def index():
    """排行榜首页"""
    scholarship_data = ScholarshipData()
    
    # 获取筛选条件
    filters = scholarship_data.get_available_filters()
    
    # 如果是学生用户，重定向到个人页面
    if current_user.is_student():
        return render_template('ranking/student_ranking.html', filters=filters)
    
    return render_template('ranking/index.html', filters=filters)

@ranking_bp.route('/api/data')
@login_required
def api_data():
    """获取排行榜数据API"""
    try:
        # 获取查询参数
        academic_year = request.args.get('academic_year')
        semester = request.args.get('semester', type=int)
        major = request.args.get('major')
        order_by = request.args.get('order_by', 'total_score')
        order_desc = request.args.get('order_desc', 'true').lower() == 'true'
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # 计算偏移量
        offset = (page - 1) * per_page
        
        scholarship_data = ScholarshipData()
        
        # 如果是学生用户，只返回自己的数据
        if current_user.is_student() and current_user.student_id:
            student_data = scholarship_data.get_student_detail(current_user.student_id)
            # 根据筛选条件过滤数据
            filtered_data = []
            for record in student_data:
                if academic_year and record['academic_year'] != academic_year:
                    continue
                if semester and record['semester'] != semester:
                    continue
                if major and major not in record['major_name']:
                    continue
                filtered_data.append(record)
            
            return jsonify({
                'success': True,
                'data': filtered_data,
                'total': len(filtered_data),
                'page': 1,
                'per_page': len(filtered_data)
            })
        
        # 获取排行榜数据
        ranking_data = scholarship_data.get_ranking_data(
            academic_year=academic_year,
            semester=semester,
            major=major,
            order_by=order_by,
            order_desc=order_desc,
            limit=per_page,
            offset=offset
        )
        
        # 获取总数（用于分页）
        total_data = scholarship_data.get_ranking_data(
            academic_year=academic_year,
            semester=semester,
            major=major
        )
        total = len(total_data)
        
        return jsonify({
            'success': True,
            'data': ranking_data,
            'total': total,
            'page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ranking_bp.route('/api/search')
@login_required
def api_search():
    """搜索学生API"""
    try:
        keyword = request.args.get('keyword', '').strip()
        academic_year = request.args.get('academic_year')
        semester = request.args.get('semester', type=int)
        
        if not keyword:
            return jsonify({
                'success': False,
                'error': '请输入搜索关键词'
            }), 400
        
        scholarship_data = ScholarshipData()
        
        # 如果是学生用户，只能搜索自己
        if current_user.is_student() and current_user.student_id:
            if keyword in current_user.student_id or (current_user.full_name and keyword in current_user.full_name):
                student_data = scholarship_data.get_student_detail(current_user.student_id)
                return jsonify({
                    'success': True,
                    'data': student_data
                })
            else:
                return jsonify({
                    'success': True,
                    'data': []
                })
        
        # 搜索学生
        search_results = scholarship_data.search_students(
            keyword=keyword,
            academic_year=academic_year,
            semester=semester
        )
        
        return jsonify({
            'success': True,
            'data': search_results
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ranking_bp.route('/export')
@login_required
def export():
    """导出排行榜数据"""
    try:
        # 获取查询参数
        academic_year = request.args.get('academic_year')
        semester = request.args.get('semester', type=int)
        major = request.args.get('major')
        order_by = request.args.get('order_by', 'total_score')
        order_desc = request.args.get('order_desc', 'true').lower() == 'true'
        
        scholarship_data = ScholarshipData()
        
        # 如果是学生用户，只导出自己的数据
        if current_user.is_student() and current_user.student_id:
            data = scholarship_data.get_student_detail(current_user.student_id)
            filename = f'my_scores_{current_user.student_id}.csv'
        else:
            # 获取所有数据（不分页）
            data = scholarship_data.get_ranking_data(
                academic_year=academic_year,
                semester=semester,
                major=major,
                order_by=order_by,
                order_desc=order_desc
            )
            
            # 生成文件名
            filename_parts = ['ranking']
            if academic_year:
                filename_parts.append(academic_year)
            if semester:
                filename_parts.append(f'semester{semester}')
            if major:
                filename_parts.append(major)
            filename = '_'.join(filename_parts) + '.csv'
        
        if not data:
            flash('没有数据可导出', 'warning')
            return redirect(request.referrer or url_for('ranking.index'))
        
        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        headers = [
            '学号', '姓名', '学年', '学期', '专业', '班级', '年级',
            '综合素质分', '学业成绩', '总分', '总排名', '等级',
            '思想品德', '社会工作', '科研创新', '集体活动', '集体建设',
            '学业排名', '学业排名百分比', '备注'
        ]
        writer.writerow(headers)
        
        # 写入数据
        for row in data:
            writer.writerow([
                row.get('student_id', ''),
                row.get('name', ''),
                row.get('academic_year', ''),
                row.get('semester', ''),
                row.get('major_name', ''),
                row.get('class_name', ''),
                row.get('grade', ''),
                row.get('comprehensive_score', ''),
                row.get('academic_score', ''),
                row.get('total_score', ''),
                row.get('total_rank', ''),
                row.get('award_level', ''),
                row.get('moral_score', ''),
                row.get('social_work_score', ''),
                row.get('research_score', ''),
                row.get('activity_total_score', ''),
                row.get('collective_score', ''),
                row.get('academic_rank', ''),
                row.get('academic_percentage', ''),
                row.get('remarks', '')
            ])
        
        # 记录导出日志
        log_user_action(
            current_user.id,
            'export_ranking',
            f'导出排行榜数据: {filename}',
            request.remote_addr,
            request.headers.get('User-Agent')
        )
        
        # 创建响应
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
        
    except Exception as e:
        flash(f'导出失败: {str(e)}', 'error')
        return redirect(request.referrer or url_for('ranking.index'))
