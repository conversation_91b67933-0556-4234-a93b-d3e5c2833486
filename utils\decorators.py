#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
装饰器模块
提供权限控制等装饰器
"""

from functools import wraps
from flask import abort, flash, redirect, url_for
from flask_login import current_user

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        
        if not current_user.is_admin():
            flash('您没有权限访问此页面', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function

def teacher_or_admin_required(f):
    """教师或管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        
        if not (current_user.is_admin() or current_user.is_teacher()):
            flash('您没有权限访问此页面', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function

def sub_admin_required(f):
    """子管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        
        if not current_user.has_admin_permission():
            flash('您没有权限访问此页面', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function

def student_data_access_required(f):
    """学生数据访问权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        
        # 获取要访问的学生ID
        student_id = kwargs.get('student_id') or args[0] if args else None
        
        # 管理员和教师可以访问所有学生数据
        if current_user.is_admin() or current_user.is_teacher():
            return f(*args, **kwargs)
        
        # 学生只能访问自己的数据
        if current_user.is_student() and current_user.student_id == student_id:
            return f(*args, **kwargs)
        
        flash('您只能查看自己的数据', 'error')
        abort(403)
    
    return decorated_function
