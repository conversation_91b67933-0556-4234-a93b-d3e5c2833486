{% extends "base.html" %}

{% block title %}我的学业数据 - 学生学业数据管理与分析平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-user-graduate me-2"></i>
            我的学业数据
        </h1>
    </div>
</div>

{% if student_data %}
<!-- 基本信息卡片 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-id-card me-2"></i>
                    基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>学号：</strong>{{ student_data[0].student_id }}
                    </div>
                    <div class="col-md-3">
                        <strong>姓名：</strong>{{ student_data[0].name }}
                    </div>
                    <div class="col-md-3">
                        <strong>专业：</strong>{{ student_data[0].major_name }}
                    </div>
                    <div class="col-md-3">
                        <strong>班级：</strong>{{ student_data[0].class_name }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 成绩统计卡片 -->
<div class="row mb-4">
    {% set latest_record = student_data[-1] %}
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ "%.2f"|format(latest_record.total_score) }}</h4>
                        <p class="card-text">最新总分</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ latest_record.total_rank or 'N/A' }}</h4>
                        <p class="card-text">最新排名</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-trophy fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ "%.2f"|format(latest_record.academic_score) }}</h4>
                        <p class="card-text">学业成绩</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ latest_record.award_level or '无' }}</h4>
                        <p class="card-text">奖学金等级</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-award fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 成绩趋势图 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    成绩趋势
                </h5>
            </div>
            <div class="card-body">
                <div id="studentTrendChart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 详细成绩记录 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    详细成绩记录
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>学年学期</th>
                                <th>总分</th>
                                <th>总排名</th>
                                <th>学业成绩</th>
                                <th>学业排名</th>
                                <th>综合素质分</th>
                                <th>奖学金等级</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in student_data %}
                            <tr>
                                <td>{{ record.academic_year }}-{{ record.semester }}</td>
                                <td>{{ "%.2f"|format(record.total_score) }}</td>
                                <td>{{ record.total_rank or 'N/A' }}</td>
                                <td>{{ "%.2f"|format(record.academic_score) }}</td>
                                <td>{{ record.academic_rank or 'N/A' }}</td>
                                <td>{{ "%.2f"|format(record.comprehensive_score) }}</td>
                                <td>
                                    {% if record.award_level and record.award_level != '无' %}
                                        <span class="badge bg-success">{{ record.award_level }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">无</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            暂无学业数据记录。
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    {% if student_data %}
    drawStudentTrendChart();
    {% endif %}
});

function drawStudentTrendChart() {
    const chart = echarts.init(document.getElementById('studentTrendChart'));
    
    const data = [
        {% for record in student_data %}
        {
            semester: '{{ record.academic_year }}-{{ record.semester }}',
            total_score: {{ record.total_score }},
            academic_score: {{ record.academic_score }},
            comprehensive_score: {{ record.comprehensive_score }},
            total_rank: {{ record.total_rank or 0 }}
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ];
    
    const option = {
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['总分', '学业成绩', '综合素质分']
        },
        xAxis: {
            type: 'category',
            data: data.map(item => item.semester)
        },
        yAxis: [
            {
                type: 'value',
                name: '分数',
                position: 'left'
            },
            {
                type: 'value',
                name: '排名',
                position: 'right',
                inverse: true
            }
        ],
        series: [
            {
                name: '总分',
                type: 'line',
                data: data.map(item => item.total_score),
                itemStyle: { color: '#4facfe' }
            },
            {
                name: '学业成绩',
                type: 'line',
                data: data.map(item => item.academic_score),
                itemStyle: { color: '#28a745' }
            },
            {
                name: '综合素质分',
                type: 'line',
                data: data.map(item => item.comprehensive_score),
                itemStyle: { color: '#17a2b8' }
            }
        ]
    };
    
    chart.setOption(option);
}
</script>
{% endblock %}
