#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员路由
处理用户管理、系统配置、日志查看等功能
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models.user import User
from models.database import get_db_cursor, get_api_config, set_api_config, log_user_action
from utils.decorators import admin_required

admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/')
@login_required
@admin_required
def index():
    """管理员首页"""
    return render_template('admin/index.html')

@admin_bp.route('/users')
@login_required
@admin_required
def users():
    """用户管理页面"""
    users = User.get_all_users()
    return render_template('admin/users.html', users=users)

@admin_bp.route('/api/users')
@login_required
@admin_required
def api_users():
    """获取用户列表API"""
    try:
        role = request.args.get('role')
        users = User.get_all_users(role=role)
        
        users_data = []
        for user in users:
            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'full_name': user.full_name,
                'role': user.role,
                'student_id': user.student_id,
                'is_active': user.is_active,
                'is_sub_admin': user.is_sub_admin,
                'created_at': user.created_at,
                'last_login': user.last_login
            })
        
        return jsonify({
            'success': True,
            'data': users_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/api/users/<int:user_id>', methods=['PUT'])
@login_required
@admin_required
def api_update_user(user_id):
    """更新用户信息API"""
    try:
        data = request.get_json()
        user = User.get_by_id(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'error': '用户不存在'
            }), 404
        
        # 更新用户信息
        user.email = data.get('email', user.email)
        user.full_name = data.get('full_name', user.full_name)
        user.role = data.get('role', user.role)
        user.is_active = data.get('is_active', user.is_active)
        user.is_sub_admin = data.get('is_sub_admin', user.is_sub_admin)
        
        if user.save():
            # 记录日志
            log_user_action(
                current_user.id,
                'user_updated',
                f'管理员更新用户信息: {user.username}',
                request.remote_addr,
                request.headers.get('User-Agent')
            )
            
            return jsonify({
                'success': True,
                'message': '用户信息更新成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '更新失败'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/api/users/<int:user_id>/reset_password', methods=['POST'])
@login_required
@admin_required
def api_reset_password(user_id):
    """重置用户密码API"""
    try:
        data = request.get_json()
        new_password = data.get('new_password', 'password123')
        
        user = User.get_by_id(user_id)
        if not user:
            return jsonify({
                'success': False,
                'error': '用户不存在'
            }), 404
        
        user.set_password(new_password)
        if user.save():
            # 记录日志
            log_user_action(
                current_user.id,
                'password_reset',
                f'管理员重置用户密码: {user.username}',
                request.remote_addr,
                request.headers.get('User-Agent')
            )
            
            return jsonify({
                'success': True,
                'message': '密码重置成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '重置失败'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/api_config')
@login_required
@admin_required
def api_config():
    """API配置管理页面"""
    return render_template('admin/api_config.html')

@admin_bp.route('/api/config')
@login_required
@admin_required
def api_get_config():
    """获取API配置"""
    try:
        with get_db_cursor() as cursor:
            cursor.execute('SELECT * FROM api_configs ORDER BY config_key')
            configs = [dict(row) for row in cursor.fetchall()]
        
        return jsonify({
            'success': True,
            'data': configs
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/api/config', methods=['POST'])
@login_required
@admin_required
def api_update_config():
    """更新API配置"""
    try:
        data = request.get_json()
        config_key = data.get('config_key')
        config_value = data.get('config_value')
        description = data.get('description')
        
        if not config_key or config_value is None:
            return jsonify({
                'success': False,
                'error': '配置键和值不能为空'
            }), 400
        
        set_api_config(config_key, config_value, description)
        
        # 记录日志
        log_user_action(
            current_user.id,
            'config_updated',
            f'更新API配置: {config_key}',
            request.remote_addr,
            request.headers.get('User-Agent')
        )
        
        return jsonify({
            'success': True,
            'message': '配置更新成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/logs')
@login_required
@admin_required
def logs():
    """系统日志页面"""
    return render_template('admin/logs.html')

@admin_bp.route('/api/logs')
@login_required
@admin_required
def api_logs():
    """获取系统日志API"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        action = request.args.get('action')
        user_id = request.args.get('user_id', type=int)
        
        offset = (page - 1) * per_page
        
        with get_db_cursor() as cursor:
            # 构建查询
            query = '''
                SELECT 
                    sl.id, sl.action, sl.description, sl.ip_address, 
                    sl.created_at, u.username, u.full_name
                FROM system_logs sl
                LEFT JOIN users u ON sl.user_id = u.id
                WHERE 1=1
            '''
            params = []
            
            if action:
                query += ' AND sl.action LIKE ?'
                params.append(f'%{action}%')
            
            if user_id:
                query += ' AND sl.user_id = ?'
                params.append(user_id)
            
            query += ' ORDER BY sl.created_at DESC LIMIT ? OFFSET ?'
            params.extend([per_page, offset])
            
            cursor.execute(query, params)
            logs = [dict(row) for row in cursor.fetchall()]
            
            # 获取总数
            count_query = '''
                SELECT COUNT(*) as total
                FROM system_logs sl
                WHERE 1=1
            '''
            count_params = []
            
            if action:
                count_query += ' AND sl.action LIKE ?'
                count_params.append(f'%{action}%')
            
            if user_id:
                count_query += ' AND sl.user_id = ?'
                count_params.append(user_id)
            
            cursor.execute(count_query, count_params)
            total = cursor.fetchone()['total']
        
        return jsonify({
            'success': True,
            'data': logs,
            'total': total,
            'page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/feedback')
@login_required
@admin_required
def feedback():
    """用户反馈管理页面"""
    return render_template('admin/feedback.html')

@admin_bp.route('/api/feedback')
@login_required
@admin_required
def api_feedback():
    """获取用户反馈API"""
    try:
        status = request.args.get('status')
        
        with get_db_cursor() as cursor:
            query = '''
                SELECT 
                    f.id, f.title, f.content, f.status, f.admin_reply,
                    f.created_at, f.updated_at, u.username, u.full_name
                FROM feedback f
                JOIN users u ON f.user_id = u.id
                WHERE 1=1
            '''
            params = []
            
            if status:
                query += ' AND f.status = ?'
                params.append(status)
            
            query += ' ORDER BY f.created_at DESC'
            
            cursor.execute(query, params)
            feedback_list = [dict(row) for row in cursor.fetchall()]
        
        return jsonify({
            'success': True,
            'data': feedback_list
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
