#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奖学金数据分析系统
解析Excel文件并存储到SQLite数据库中
"""

import pandas as pd
import sqlite3
import os
import re
from pathlib import Path
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class ScholarshipAnalyzer:
    def __init__(self, db_path="scholarship_data.db"):
        """初始化分析器"""
        self.db_path = db_path
        self.conn = None
        self.init_database()

    def init_database(self):
        """初始化数据库结构"""
        self.conn = sqlite3.connect(self.db_path)
        cursor = self.conn.cursor()

        # 创建学期信息表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS semesters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                academic_year TEXT NOT NULL,
                semester INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(academic_year, semester)
            )
        """
        )

        # 创建学生基本信息表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS students (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id TEXT NOT NULL UNIQUE,
                name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        )

        # 创建专业信息表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS majors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                major_name TEXT NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        )

        # 创建成绩记录表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS scores (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id TEXT NOT NULL,
                semester_id INTEGER NOT NULL,
                major_id INTEGER NOT NULL,
                class_name TEXT,
                grade INTEGER,
                
                -- 综合素质测评各项目
                moral_score REAL DEFAULT 0,           -- 思想品德评估(25分)
                social_work_score REAL DEFAULT 0,     -- 社会工作(15分)
                research_score REAL DEFAULT 0,        -- 科研及科技创新(25分)
                activity_base_score REAL DEFAULT 0,   -- 大型集体活动基础分
                activity_exercise_score REAL DEFAULT 0, -- 早操/晚自习
                activity_event_score REAL DEFAULT 0,  -- 未设奖/设奖活动
                activity_total_score REAL DEFAULT 0,  -- 活动总分(20分)
                collective_score REAL DEFAULT 0,      -- 集体建设(15分)
                
                -- 综合成绩
                comprehensive_score REAL DEFAULT 0,   -- 综合素质测评总成绩
                academic_score REAL DEFAULT 0,        -- 学业成绩
                academic_rank INTEGER,                 -- 学业成绩排名
                academic_percentage REAL,              -- 学业成绩排名百分比
                
                -- 总评
                comprehensive_weighted REAL DEFAULT 0, -- 综合素质测评总成绩×35%
                academic_weighted REAL DEFAULT 0,      -- 学业成绩×65%
                total_score REAL DEFAULT 0,           -- 总评
                total_rank INTEGER,                    -- 总分专业排名
                
                -- 其他信息
                remarks TEXT,                          -- 备注
                award_level TEXT,                      -- 拟定等级
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (semester_id) REFERENCES semesters (id),
                FOREIGN KEY (major_id) REFERENCES majors (id),
                UNIQUE(student_id, semester_id, major_id)
            )
        """
        )

        # 创建文件处理记录表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS file_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_path TEXT NOT NULL UNIQUE,
                file_hash TEXT,
                processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                record_count INTEGER DEFAULT 0
            )
        """
        )

        self.conn.commit()
        logger.info("数据库初始化完成")

    def parse_title_info(self, title_text):
        """解析标题中的学年、学期、年级、专业信息"""
        if not title_text or pd.isna(title_text):
            return None, None, None, None

        title_text = str(title_text).strip()

        # 匹配模式：学年-学期-年级-专业
        patterns = [
            r"(\d{4}-\d{4}).*?第([一二])学期.*?(\d+)级.*?([^奖]+)奖学金",
            r"(\d{4}-\d{4}).*?第(\d)学期.*?(\d+)级.*?([^奖]+)奖学金",
        ]

        for pattern in patterns:
            match = re.search(pattern, title_text)
            if match:
                academic_year = match.group(1)
                semester_text = match.group(2)
                grade = match.group(3)
                major = match.group(4).strip()

                # 转换学期
                if semester_text == "一" or semester_text == "1":
                    semester = 1
                elif semester_text == "二" or semester_text == "2":
                    semester = 2
                else:
                    semester = None

                # 标准化专业名称
                major_mapping = {"计科": "计算机科学与技术", "电信": "电子信息工程", "通信": "通信工程"}
                major = major_mapping.get(major, major)  # 如果major在mapping中，则替换，否则保持原样

                return academic_year, semester, grade, major

        logger.warning(f"无法解析标题: {title_text}")
        return None, None, None, None

    def get_or_create_semester(self, academic_year, semester):
        """获取或创建学期记录"""
        cursor = self.conn.cursor()
        cursor.execute(
            "SELECT id FROM semesters WHERE academic_year = ? AND semester = ?",
            (academic_year, semester),
        )
        result = cursor.fetchone()

        if result:
            return result[0]
        else:
            cursor.execute(
                "INSERT INTO semesters (academic_year, semester) VALUES (?, ?)",
                (academic_year, semester),
            )
            self.conn.commit()
            return cursor.lastrowid

    def get_or_create_major(self, major_name):
        """获取或创建专业记录"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT id FROM majors WHERE major_name = ?", (major_name,))
        result = cursor.fetchone()

        if result:
            return result[0]
        else:
            cursor.execute("INSERT INTO majors (major_name) VALUES (?)", (major_name,))
            self.conn.commit()
            return cursor.lastrowid

    def get_or_create_student(self, student_id, name):
        """获取或创建学生记录"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT id FROM students WHERE student_id = ?", (student_id,))
        result = cursor.fetchone()

        if result:
            # 更新学生姓名（可能有变化）
            cursor.execute(
                "UPDATE students SET name = ?, updated_at = CURRENT_TIMESTAMP WHERE student_id = ?",
                (name, student_id),
            )
            self.conn.commit()
            return result[0]
        else:
            cursor.execute(
                "INSERT INTO students (student_id, name) VALUES (?, ?)",
                (student_id, name),
            )
            self.conn.commit()
            return cursor.lastrowid

    def safe_float(self, value):
        """安全转换为浮点数"""
        if pd.isna(value) or value == "" or value is None:
            return 0.0
        try:
            return float(value)
        except (ValueError, TypeError):
            return 0.0

    def safe_int(self, value):
        """安全转换为整数"""
        if pd.isna(value) or value == "" or value is None:
            return None
        try:
            return int(float(value))
        except (ValueError, TypeError):
            return None

    def parse_worksheet(self, file_path, sheet_name, df):
        """解析单个工作表"""
        logger.info(f"解析工作表: {sheet_name}")

        if len(df) < 5:
            logger.warning(f"工作表 {sheet_name} 数据行数不足，跳过")
            return 0

        # 解析标题信息
        title_row = df.iloc[0]
        title_text = ""
        for cell in title_row:
            if pd.notna(cell) and str(cell).strip():
                title_text = str(cell).strip()
                break

        academic_year, semester, grade, major = self.parse_title_info(title_text)
        if not all([academic_year, semester, grade, major]):
            logger.error(f"无法解析工作表 {sheet_name} 的标题信息: {title_text}")
            return 0

        # 获取或创建相关记录
        semester_id = self.get_or_create_semester(academic_year, semester)
        major_id = self.get_or_create_major(major)

        # 解析数据行（从第5行开始）
        record_count = 0
        for idx in range(4, len(df)):  # 从第5行开始（索引4）
            row = df.iloc[idx]

            # 检查是否为有效数据行
            student_id = row.iloc[0] if len(row) > 0 else None
            if pd.isna(student_id) or str(student_id).strip() == "":
                continue

            student_id = str(student_id).strip()
            name = (
                str(row.iloc[1]).strip()
                if len(row) > 1 and pd.notna(row.iloc[1])
                else ""
            )
            class_name = (
                str(row.iloc[2]).strip()
                if len(row) > 2 and pd.notna(row.iloc[2])
                else ""
            )

            if not name:
                continue

            # 获取或创建学生记录
            self.get_or_create_student(student_id, name)

            # 解析各项成绩
            moral_score = self.safe_float(row.iloc[3]) if len(row) > 3 else 0.0
            social_work_score = self.safe_float(row.iloc[4]) if len(row) > 4 else 0.0
            research_score = self.safe_float(row.iloc[5]) if len(row) > 5 else 0.0
            activity_base_score = self.safe_float(row.iloc[6]) if len(row) > 6 else 0.0
            activity_exercise_score = (
                self.safe_float(row.iloc[7]) if len(row) > 7 else 0.0
            )
            activity_event_score = self.safe_float(row.iloc[8]) if len(row) > 8 else 0.0
            activity_total_score = self.safe_float(row.iloc[9]) if len(row) > 9 else 0.0
            collective_score = self.safe_float(row.iloc[10]) if len(row) > 10 else 0.0

            # 应用分数上限
            moral_score = min(moral_score, 25.0)
            social_work_score = min(social_work_score, 15.0)
            research_score = min(research_score, 25.0)
            activity_total_score = min(activity_total_score, 20.0)
            collective_score = min(collective_score, 15.0)

            comprehensive_score = (
                self.safe_float(row.iloc[11]) if len(row) > 11 else 0.0
            )
            academic_score = self.safe_float(row.iloc[12]) if len(row) > 12 else 0.0
            academic_rank = self.safe_int(row.iloc[13]) if len(row) > 13 else None
            academic_percentage = (
                self.safe_float(row.iloc[14]) if len(row) > 14 else None
            )

            comprehensive_weighted = (
                self.safe_float(row.iloc[15]) if len(row) > 15 else 0.0
            )
            academic_weighted = self.safe_float(row.iloc[16]) if len(row) > 16 else 0.0
            total_score = self.safe_float(row.iloc[17]) if len(row) > 17 else 0.0
            total_rank = self.safe_int(row.iloc[18]) if len(row) > 18 else None

            remarks = (
                str(row.iloc[19]).strip()
                if len(row) > 19 and pd.notna(row.iloc[19])
                else ""
            )
            award_level = (
                str(row.iloc[20]).strip()
                if len(row) > 20 and pd.notna(row.iloc[20])
                else ""
            )

            # 插入或更新成绩记录
            cursor = self.conn.cursor()
            cursor.execute(
                """
                INSERT OR REPLACE INTO scores (
                    student_id, semester_id, major_id, class_name, grade,
                    moral_score, social_work_score, research_score,
                    activity_base_score, activity_exercise_score, activity_event_score, activity_total_score,
                    collective_score, comprehensive_score, academic_score, academic_rank, academic_percentage,
                    comprehensive_weighted, academic_weighted, total_score, total_rank,
                    remarks, award_level, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """,
                (
                    student_id,
                    semester_id,
                    major_id,
                    class_name,
                    int(grade),
                    moral_score,
                    social_work_score,
                    research_score,
                    activity_base_score,
                    activity_exercise_score,
                    activity_event_score,
                    activity_total_score,
                    collective_score,
                    comprehensive_score,
                    academic_score,
                    academic_rank,
                    academic_percentage,
                    comprehensive_weighted,
                    academic_weighted,
                    total_score,
                    total_rank,
                    remarks,
                    award_level,
                ),
            )

            record_count += 1

        self.conn.commit()
        logger.info(f"工作表 {sheet_name} 解析完成，共处理 {record_count} 条记录")
        return record_count

    def get_file_hash(self, file_path):
        """计算文件哈希值"""
        import hashlib

        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def is_file_processed(self, file_path):
        """检查文件是否已处理"""
        if not os.path.exists(file_path):
            return False

        current_hash = self.get_file_hash(file_path)
        cursor = self.conn.cursor()
        cursor.execute(
            "SELECT file_hash FROM file_records WHERE file_path = ?", (file_path,)
        )
        result = cursor.fetchone()

        if result and result[0] == current_hash:
            return True
        return False

    def mark_file_processed(self, file_path, record_count):
        """标记文件已处理"""
        file_hash = self.get_file_hash(file_path)
        cursor = self.conn.cursor()
        cursor.execute(
            """
            INSERT OR REPLACE INTO file_records (file_path, file_hash, record_count, processed_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        """,
            (file_path, file_hash, record_count),
        )
        self.conn.commit()

    def process_excel_file(self, file_path, force_update=False):
        """处理单个Excel文件"""
        logger.info(f"开始处理文件: {file_path}")

        if not force_update and self.is_file_processed(file_path):
            logger.info(f"文件 {file_path} 已处理过，跳过")
            return 0

        try:
            excel_file = pd.ExcelFile(file_path)
            total_records = 0

            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
                records = self.parse_worksheet(file_path, sheet_name, df)
                total_records += records

            self.mark_file_processed(file_path, total_records)
            logger.info(f"文件 {file_path} 处理完成，共处理 {total_records} 条记录")
            return total_records

        except Exception as e:
            logger.error(f"处理文件 {file_path} 时出错: {e}")
            return 0

    def process_all_files(self, file_list, force_update=False):
        """处理所有Excel文件"""
        total_records = 0
        processed_files = 0

        for file_path in file_list:
            if os.path.exists(file_path):
                records = self.process_excel_file(file_path, force_update)
                total_records += records
                if records > 0:
                    processed_files += 1
            else:
                logger.warning(f"文件不存在: {file_path}")

        logger.info(f"批量处理完成: 处理了 {processed_files} 个文件，共 {total_records} 条记录")
        return total_records

    def query_student_scores(
        self, student_id=None, name=None, academic_year=None, semester=None, major=None
    ):
        """查询学生成绩"""
        cursor = self.conn.cursor()

        query = """
            SELECT
                s.student_id, s.name, sem.academic_year, sem.semester, m.major_name,
                sc.class_name, sc.grade, sc.comprehensive_score, sc.academic_score,
                sc.total_score, sc.total_rank, sc.award_level, sc.remarks
            FROM scores sc
            JOIN students s ON sc.student_id = s.student_id
            JOIN semesters sem ON sc.semester_id = sem.id
            JOIN majors m ON sc.major_id = m.id
            WHERE 1=1
        """

        params = []
        if student_id:
            query += " AND s.student_id = ?"
            params.append(student_id)
        if name:
            query += " AND s.name LIKE ?"
            params.append(f"%{name}%")
        if academic_year:
            query += " AND sem.academic_year = ?"
            params.append(academic_year)
        if semester:
            query += " AND sem.semester = ?"
            params.append(semester)
        if major:
            query += " AND m.major_name LIKE ?"
            params.append(f"%{major}%")

        query += (
            " ORDER BY sem.academic_year, sem.semester, m.major_name, sc.total_rank"
        )

        cursor.execute(query, params)
        return cursor.fetchall()

    def get_top_students(self, academic_year=None, semester=None, major=None, limit=10):
        """获取排名前列的学生"""
        cursor = self.conn.cursor()

        query = """
            SELECT
                s.student_id, s.name, sem.academic_year, sem.semester, m.major_name,
                sc.total_score, sc.total_rank, sc.award_level
            FROM scores sc
            JOIN students s ON sc.student_id = s.student_id
            JOIN semesters sem ON sc.semester_id = sem.id
            JOIN majors m ON sc.major_id = m.id
            WHERE sc.total_rank IS NOT NULL
        """

        params = []
        if academic_year:
            query += " AND sem.academic_year = ?"
            params.append(academic_year)
        if semester:
            query += " AND sem.semester = ?"
            params.append(semester)
        if major:
            query += " AND m.major_name LIKE ?"
            params.append(f"%{major}%")

        query += " ORDER BY sem.academic_year DESC, sem.semester DESC, m.major_name, sc.total_rank LIMIT ?"
        params.append(limit)

        cursor.execute(query, params)
        return cursor.fetchall()

    def get_statistics(self):
        """获取数据库统计信息"""
        cursor = self.conn.cursor()

        # 学生总数
        cursor.execute("SELECT COUNT(*) FROM students")
        student_count = cursor.fetchone()[0]

        # 成绩记录总数
        cursor.execute("SELECT COUNT(*) FROM scores")
        score_count = cursor.fetchone()[0]

        # 学期数
        cursor.execute("SELECT COUNT(*) FROM semesters")
        semester_count = cursor.fetchone()[0]

        # 专业数
        cursor.execute("SELECT COUNT(*) FROM majors")
        major_count = cursor.fetchone()[0]

        # 已处理文件数
        cursor.execute("SELECT COUNT(*) FROM file_records")
        file_count = cursor.fetchone()[0]

        return {
            "students": student_count,
            "scores": score_count,
            "semesters": semester_count,
            "majors": major_count,
            "files": file_count,
        }

    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            logger.info("数据库连接已关闭")


def main():
    """主函数"""
    # Excel文件路径列表
    excel_files = [
        "计工5学期/2022-2023_1.xlsx",
        "计工5学期/2022-2023_2.xlsx",
        "计工5学期/2023-2024_1.xlsx",
        "计工5学期/2023-2024_2.xlsx",
        "计工5学期/2024-2025_1.xlsx",
    ]

    # 创建分析器实例
    analyzer = ScholarshipAnalyzer()

    try:
        print("=== 奖学金数据分析系统 ===")
        print("开始处理Excel文件...")

        # 处理所有文件
        total_records = analyzer.process_all_files(excel_files)

        print(f"\n处理完成！共导入 {total_records} 条记录")

        # 显示统计信息
        stats = analyzer.get_statistics()
        print(f"\n=== 数据库统计信息 ===")
        print(f"学生总数: {stats['students']}")
        print(f"成绩记录数: {stats['scores']}")
        print(f"学期数: {stats['semesters']}")
        print(f"专业数: {stats['majors']}")
        print(f"已处理文件数: {stats['files']}")

        # 示例查询
        print(f"\n=== 示例查询 ===")

        # 查询2024-2025学年第一学期的前10名学生
        print("\n2024-2025学年第一学期各专业前10名:")
        top_students = analyzer.get_top_students(
            academic_year="2024-2025", semester=1, limit=10
        )
        for student in top_students:
            print(
                f"  {student[1]} ({student[0]}) - {student[4]} - 总分: {student[5]:.2f} - 排名: {student[6]} - {student[7] or '无等级'}"
            )

        # 查询特定学生的所有成绩
        print(f"\n查询学生成绩示例 (如果有数据):")
        if total_records > 0:
            # 随机查询一个学生
            cursor = analyzer.conn.cursor()
            cursor.execute("SELECT student_id, name FROM students LIMIT 1")
            sample_student = cursor.fetchone()
            if sample_student:
                student_scores = analyzer.query_student_scores(
                    student_id=sample_student[0]
                )
                print(f"学生 {sample_student[1]} ({sample_student[0]}) 的成绩记录:")
                for score in student_scores:
                    print(
                        f"  {score[2]}-{score[3]} {score[4]} - 总分: {score[9]:.2f} - 排名: {score[10]}"
                    )

        print(f"\n数据已保存到数据库文件: {analyzer.db_path}")
        print("可以使用SQL工具或编写查询脚本进一步分析数据")

    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        print(f"错误: {e}")
    finally:
        analyzer.close()


if __name__ == "__main__":
    main()
