# Instructions

During your interaction with the user, if you find anything reusable in this project (e.g. version of a library, model name), especially about a fix to a mistake you made or a correction you received, you should take note in the `Lessons` section in the `.cursorrules` file so you will not make the same mistake again. 

You should also use the `.cursorrules` file as a Scratchpad to organize your thoughts. Especially when you receive a new task, you should first review the content of the Scratchpad, clear old different task if necessary, first explain the task, and plan the steps you need to take to complete the task. You can use todo markers to indicate the progress, e.g.
[X] Task 1
[ ] Task 2

Also update the progress of the task in the Scratchpad when you finish a subtask.
Especially when you finished a milestone, it will help to improve your depth of task accomplishment to use the Scratchpad to reflect and plan.
The goal is to help you maintain a big picture as well as the progress of the task. Always refer to the Scratchpad when you plan the next step.

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Always use (activate) it when doing python development. First, to check whether 'uv' is available, use `which uv`. If that's the case, first activate the venv, and then use `uv pip install` to install packages. Otherwise, fall back to `pip`.
- Due to <PERSON>urs<PERSON>'s limit, when you use `git` and `gh` and need to submit a multiline commit message, first write the message in a file, and then use `git commit -F <filename>` or similar command to commit. And then remove the file. Include "[Cursor] " in the commit message and PR title.

## Cursor learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- When using seaborn styles in matplotlib, use 'seaborn-v0_8' instead of 'seaborn' as the style name due to recent seaborn version changes
- The user is using Windows and cannot use "&&" to connect two commands.

# Scratchpad

## 当前任务：学生学业数据管理与分析平台开发

### 任务理解
基于 prd.txt 技术文档，需要实现一个完整的学生学业数据管理与分析平台，包含：
- 后端：Python Flask
- 前端：HTML + CSS + JS
- 图表：ECharts
- 设计风格：蓝白色调现代化界面
- 现有基础：scholarship_analyzer.py（数据解析）+ scholarship_data.db（SQLite数据库）

### 主要功能模块
1. 排行榜显示（支持排序、导出、AI辅助SQL查询）
2. 详情与分析（学生个人分析、智能分析报告）
3. 主面板（数据概览、统计信息）
4. API管理系统（deepseek-r1模型配置）
5. 数据库日志系统
6. 导入导出模块
7. 班级分析页面
8. 用户管理（管理员/教师/学生权限）

### 开发计划
[ ] 1. 项目结构设计和初始化
[ ] 2. Flask后端框架搭建
[ ] 3. 用户认证和权限管理系统
[ ] 4. 数据库扩展（用户表、日志表等）
[ ] 5. 排行榜功能实现
[ ] 6. 学生详情和分析功能
[ ] 7. 主面板和统计功能
[ ] 8. AI集成（deepseek-r1）
[ ] 9. 班级分析功能
[ ] 10. 导入导出功能
[ ] 11. 前端界面开发
[ ] 12. 测试和优化