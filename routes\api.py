#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API路由
提供通用的API接口
"""

from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from models.scholarship import ScholarshipData
from models.database import get_db_cursor, log_user_action
from services.ai_service import AIService

api_bp = Blueprint('api', __name__)

@api_bp.route('/sql_assistant', methods=['POST'])
@login_required
def sql_assistant():
    """AI SQL助手"""
    # 只有管理员和教师可以使用
    if current_user.is_student():
        return jsonify({'success': False, 'error': '权限不足'}), 403
    
    try:
        data = request.get_json()
        description = data.get('description', '').strip()
        
        if not description:
            return jsonify({
                'success': False,
                'error': '请输入查询描述'
            }), 400
        
        # 调用AI服务生成SQL
        ai_service = AIService()
        sql_query = ai_service.generate_sql_query(description)
        
        # 记录日志
        log_user_action(
            current_user.id,
            'sql_generated',
            f'AI生成SQL查询: {description}',
            request.remote_addr,
            request.headers.get('User-Agent')
        )
        
        return jsonify({
            'success': True,
            'data': {
                'sql': sql_query,
                'description': description
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/execute_sql', methods=['POST'])
@login_required
def execute_sql():
    """执行SQL查询"""
    # 只有管理员和子管理员可以执行SQL
    if not current_user.has_admin_permission():
        return jsonify({'success': False, 'error': '权限不足'}), 403
    
    try:
        data = request.get_json()
        sql_query = data.get('sql', '').strip()
        
        if not sql_query:
            return jsonify({
                'success': False,
                'error': '请输入SQL查询语句'
            }), 400
        
        # 安全检查：只允许SELECT查询
        sql_upper = sql_query.upper().strip()
        if not sql_upper.startswith('SELECT'):
            return jsonify({
                'success': False,
                'error': '只允许执行SELECT查询'
            }), 400
        
        # 禁止的关键词
        forbidden_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE']
        for keyword in forbidden_keywords:
            if keyword in sql_upper:
                return jsonify({
                    'success': False,
                    'error': f'不允许使用 {keyword} 语句'
                }), 400
        
        # 执行查询
        with get_db_cursor() as cursor:
            cursor.execute(sql_query)
            results = [dict(row) for row in cursor.fetchall()]
        
        # 记录日志
        log_user_action(
            current_user.id,
            'sql_executed',
            f'执行SQL查询: {sql_query[:100]}...',
            request.remote_addr,
            request.headers.get('User-Agent')
        )
        
        return jsonify({
            'success': True,
            'data': {
                'results': results,
                'count': len(results)
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'SQL执行错误: {str(e)}'
        }), 500

@api_bp.route('/filters')
@login_required
def filters():
    """获取筛选条件"""
    try:
        scholarship_data = ScholarshipData()
        filters = scholarship_data.get_available_filters()
        
        return jsonify({
            'success': True,
            'data': filters
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/search_suggestions')
@login_required
def search_suggestions():
    """搜索建议"""
    try:
        query = request.args.get('q', '').strip()
        
        if len(query) < 2:
            return jsonify({
                'success': True,
                'data': []
            })
        
        suggestions = []
        
        # 如果是学生，只返回自己的信息
        if current_user.is_student() and current_user.student_id:
            if query in current_user.student_id or (current_user.full_name and query in current_user.full_name):
                suggestions.append({
                    'type': 'student',
                    'value': current_user.student_id,
                    'label': f"{current_user.full_name} ({current_user.student_id})"
                })
        else:
            # 搜索学生
            with get_db_cursor() as cursor:
                cursor.execute('''
                    SELECT DISTINCT student_id, name
                    FROM students
                    WHERE student_id LIKE ? OR name LIKE ?
                    LIMIT 10
                ''', (f'%{query}%', f'%{query}%'))
                
                for row in cursor.fetchall():
                    suggestions.append({
                        'type': 'student',
                        'value': row['student_id'],
                        'label': f"{row['name']} ({row['student_id']})"
                    })
        
        return jsonify({
            'success': True,
            'data': suggestions
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/submit_feedback', methods=['POST'])
@login_required
def submit_feedback():
    """提交用户反馈"""
    try:
        data = request.get_json()
        title = data.get('title', '').strip()
        content = data.get('content', '').strip()
        
        if not title or not content:
            return jsonify({
                'success': False,
                'error': '标题和内容不能为空'
            }), 400
        
        # 插入反馈记录
        with get_db_cursor() as cursor:
            cursor.execute('''
                INSERT INTO feedback (user_id, title, content)
                VALUES (?, ?, ?)
            ''', (current_user.id, title, content))
        
        # 记录日志
        log_user_action(
            current_user.id,
            'feedback_submitted',
            f'提交反馈: {title}',
            request.remote_addr,
            request.headers.get('User-Agent')
        )
        
        return jsonify({
            'success': True,
            'message': '反馈提交成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/my_feedback')
@login_required
def my_feedback():
    """获取我的反馈"""
    try:
        with get_db_cursor() as cursor:
            cursor.execute('''
                SELECT id, title, content, status, admin_reply, created_at, updated_at
                FROM feedback
                WHERE user_id = ?
                ORDER BY created_at DESC
            ''', (current_user.id,))
            
            feedback_list = [dict(row) for row in cursor.fetchall()]
        
        return jsonify({
            'success': True,
            'data': feedback_list
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/statistics/summary')
@login_required
def statistics_summary():
    """获取统计摘要"""
    try:
        scholarship_data = ScholarshipData()
        stats = scholarship_data.get_statistics()
        
        # 如果是学生用户，只返回基础统计
        if current_user.is_student():
            return jsonify({
                'success': True,
                'data': {
                    'total_semesters': stats.get('semesters', 0),
                    'total_majors': stats.get('majors', 0)
                }
            })
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
